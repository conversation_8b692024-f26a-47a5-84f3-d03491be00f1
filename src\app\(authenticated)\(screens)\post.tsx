import HashtagIcon from '@assets/svgs/hashtag-icon.svg';
import InfoButton from '@assets/svgs/info-button.svg';
import PollIcon from '@assets/svgs/poll-icon.svg';
import {Text, View, TextInput, Button} from '@components/native';
import Switch from '@components/native/Switch';
import {ScreenWrapper} from '@components/shared';
import {StackBackButton} from '@components/shared';
import {Assets} from '@constants';
import {useTheme} from '@context';
import {ThemeColors} from '@types';
import {useLocalSearchParams} from 'expo-router';
import {useRouter} from 'expo-router';
import React from 'react';
import {useState} from 'react';
import {Image} from 'react-native';
import {Pressable} from 'react-native-gesture-handler';

const PollView = () => {
    const [options, setOptions] = useState(['', '']);

    const handleAddOption = () => {
        if (options.length < 4) {
            setOptions([...options, '']);
        }
    };

    const handleOptionChange = (text: string, idx: number) => {
        const newOptions = [...options];
        newOptions[idx] = text;
        setOptions(newOptions);
    };

    return (
        <View
            flex={1}
            jc="space-between"
        >
            <View>
                <View
                    fd="row"
                    ai="center"
                    mb={24}
                >
                    <InfoButton
                        width={16}
                        height={16}
                    />
                    <Text
                        fw="500"
                        fs="12"
                        color="neutral80"
                        ml={11}
                    >
                        Polls are editable only till 24 hours of posting
                    </Text>
                </View>
                <TextInput
                    title="Question"
                    placeholder="Ask a question"
                    charLimit={25}
                    gapBottom={24}
                />
                <View
                    fd="row"
                    ai="center"
                    jc="space-between"
                >
                    <Text
                        fw="400"
                        fs="12"
                        color="purple500"
                        ml={9}
                        mb={4}
                    >
                        Options
                    </Text>
                </View>
                <View>
                    {options.map((item, index) => (
                        <TextInput
                            key={index}
                            placeholder={`Option ${index + 1}`}
                            value={item}
                            onChangeText={(text) => handleOptionChange(text, index)}
                            gapBottom={12}
                        />
                    ))}
                    {options.length < 4 && (
                        <Button
                            bw={1}
                            bc="purple500"
                            bg="transparent"
                            color="purple700"
                            isFullWidth={true}
                            onPress={handleAddOption}
                            disabled={options.length >= 4}
                        >
                            + ADD OPTION
                        </Button>
                    )}
                </View>
            </View>
            <Button
                mb={53}
                isFullWidth={true}
                onPress={() => {}}
            >
                ADD
            </Button>
        </View>
    );
};

const PostView = ({setPoll, colors}: {setPoll: (value: boolean) => void; colors: ThemeColors}) => {
    const [allowComments, setAllowComments] = useState(false);
    const {push} = useRouter();

    return (
        <View
            flex={1}
            jc="space-between"
        >
            <View>
                <View
                    fd="row"
                    ai="center"
                    mb={31}
                >
                    <Image
                        style={{width: 50, height: 50, borderWidth: 1, borderRadius: 36, marginRight: 11, borderColor: colors.neutral10}}
                        source={Assets.placeholder.avatar}
                    />
                    <Text
                        fw="600"
                        fs="14"
                        color="neutral80"
                    >
                        @luca_b_2025
                    </Text>
                </View>
                <TextInput
                    placeholder="Add Title"
                    gapBottom={33}
                />
                <TextInput
                    placeholder="Add Description"
                    gapBottom={16}
                    multiline={true}
                />
                <View
                    fd="row"
                    ai="center"
                >
                    <Pressable
                        onPress={() =>
                            push({
                                pathname: '/(authenticated)/(screens)/hashtag',
                            })
                        }
                    >
                        <HashtagIcon
                            width={24}
                            height={24}
                        />
                    </Pressable>
                    <Pressable onPress={() => setPoll(true)}>
                        <PollIcon
                            width={24}
                            height={24}
                            style={{marginLeft: 25}}
                        />
                    </Pressable>
                </View>
                <View
                    fd="row"
                    ai="center"
                    jc="space-between"
                    mt={25}
                >
                    <Text
                        fw="400"
                        fs="12"
                        color="neutral70"
                    >
                        Disable Comments
                    </Text>
                    <Switch
                        value={allowComments}
                        onValueChange={setAllowComments}
                    />
                </View>
            </View>
            <Button
                mb={53}
                isFullWidth={true}
                onPress={() => {}}
            >
                POST
            </Button>
        </View>
    );
};

const PostScreen = () => {
    const params = useLocalSearchParams<{
        poll: string;
    }>();

    const [poll, setPoll] = useState(Boolean(params.poll === 'true'));
    const {colors} = useTheme();

    return (
        <ScreenWrapper>
            <View
                px={20}
                flex={1}
            >
                <View
                    pb={16}
                    pt={48}
                >
                    <StackBackButton title={poll ? 'Create a Poll' : 'New Post'} />
                </View>
                {poll ? (
                    <PollView />
                ) : (
                    <PostView
                        setPoll={setPoll}
                        colors={colors}
                    />
                )}
            </View>
        </ScreenWrapper>
    );
};

export default PostScreen;
