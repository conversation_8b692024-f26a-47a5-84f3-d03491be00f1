import {View} from '@components/native';
import {useTheme} from '@context';
import React, {useEffect, useRef} from 'react';
import {Animated, StyleSheet, TouchableOpacity, ViewStyle} from 'react-native';

interface CustomSwitchProps {
    value: boolean;
    onValueChange: (value: boolean) => void;
    disabled?: boolean;
    style?: ViewStyle;
}

const TRACK_WIDTH = 61;
const TRACK_HEIGHT = 32;
const THUMB_SIZE = 25;
const PADDING = (TRACK_HEIGHT - THUMB_SIZE) / 2;

const Switch: React.FC<CustomSwitchProps> = ({value, onValueChange, disabled, style}) => {
    const {colors} = useTheme();

    const onColor = colors.purple500;
    const offColor = colors.neutral30;
    const trackColor = colors.neutral00;
    const borderColor = value ? onColor : offColor;
    const thumbColor = value ? onColor : offColor;

    const anim = useRef(new Animated.Value(value ? 1 : 0)).current;

    useEffect(() => {
        Animated.timing(anim, {
            toValue: value ? 1 : 0,
            duration: 180,
            useNativeDriver: false,
        }).start();
    }, [value]);

    const thumbTranslate = anim.interpolate({
        inputRange: [0, 1],
        outputRange: [PADDING, TRACK_WIDTH - THUMB_SIZE - PADDING],
    });

    return (
        <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => !disabled && onValueChange(!value)}
            disabled={disabled}
            style={[{opacity: disabled ? 0.5 : 1}, style]}
            accessibilityRole="switch"
            accessibilityState={{checked: value, disabled}}
        >
            <View
                style={[
                    styles.track,
                    {
                        backgroundColor: trackColor,
                        borderColor: borderColor,
                    },
                ]}
            >
                <Animated.View
                    style={[
                        styles.thumb,
                        {
                            backgroundColor: thumbColor,
                            transform: [{translateX: thumbTranslate}],
                            borderColor: borderColor,
                        },
                    ]}
                />
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    track: {
        width: TRACK_WIDTH,
        height: TRACK_HEIGHT,
        borderRadius: TRACK_HEIGHT / 2,
        borderWidth: 1,
        justifyContent: 'center',
        backgroundColor: 'neutral00',
        borderColor: 'neutral30',
    },
    thumb: {
        width: THUMB_SIZE,
        height: THUMB_SIZE,
        borderRadius: THUMB_SIZE / 2,
        position: 'absolute',
        left: 0,
        borderWidth: 1,
        backgroundColor: 'neutral30',
        borderColor: 'neutral30',
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 1},
        shadowOpacity: 0.1,
        shadowRadius: 1,
        elevation: 1,
    },
});

export default Switch;
