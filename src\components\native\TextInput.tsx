import {Text, View} from '@components/native';
import {useTheme} from '@context';
import {EvilIcons, Feather} from '@expo/vector-icons';
import React from 'react';
import {NativeSyntheticEvent, Pressable, TextInput as RNTextInput, TextInputProps as RNTextInputProps, StyleSheet, TextInputFocusEventData} from 'react-native';

export interface FloatingTextInputProps extends RNTextInputProps {
    title?: string;
    placeholder?: string;
    gapBottom?: number;
    suffixIcon?: React.ReactNode;
    isPassword?: boolean;
    charLimit?: number;
    multiline?: boolean; // New prop, default false
}

export const TextInput = React.forwardRef<RNTextInput, FloatingTextInputProps>(
    ({placeholder, title, value, onFocus, onBlur, suffixIcon, isPassword, charLimit, gapBottom = 12, style, multiline = false, ...props}, ref) => {
        const {colors} = useTheme();
        const [isFocused, setIsFocused] = React.useState(false);
        const [isPasswordVisible, setPasswordVisible] = React.useState(false);

        const handleFocus = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
            setIsFocused(true);
            onFocus?.(e);
        };

        const handleBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
            setIsFocused(false);
            onBlur?.(e);
        };

        const renderSuffixIcon = () => {
            if (suffixIcon) {
                return suffixIcon;
            }

            if (isPassword) {
                return (
                    <Pressable onPress={() => setPasswordVisible((prev) => !prev)}>
                        <Feather
                            name={isPasswordVisible ? 'eye' : 'eye-off'}
                            color="#fff"
                            size={20}
                        />
                    </Pressable>
                );
            }

            return value ? (
                <Pressable
                    onPress={() => {
                        if (props.onChangeText) {
                            props.onChangeText('');
                        }
                    }}
                >
                    <EvilIcons
                        color={colors.neutral50}
                        size={20}
                        name="close"
                    />
                </Pressable>
            ) : null;
        };

        return (
            <>
                {title && (
                    <View
                        ml={9}
                        mb={4}
                    >
                        <Text
                            fw="400"
                            fs="12"
                            color="purple500"
                        >
                            {title}
                        </Text>
                    </View>
                )}
                <View
                    h={multiline ? 185 : undefined}
                    style={[
                        styles.container,
                        {
                            backgroundColor: colors.neutral00,
                        },
                    ]}
                >
                    <View style={styles.inputWrapper}>
                        <RNTextInput
                            ref={ref}
                            value={value}
                            secureTextEntry={isPassword && !isPasswordVisible}
                            style={[
                                styles.input,
                                {
                                    flex: 1,
                                    color: colors.neutral80,
                                    textAlignVertical: 'center', // Center text vertically
                                },
                                style,
                            ]}
                            onFocus={handleFocus}
                            onBlur={handleBlur}
                            selectionColor={colors.purple700}
                            placeholder={isFocused ? '' : placeholder}
                            placeholderTextColor={colors.neutral30}
                            maxLength={charLimit}
                            multiline={multiline}
                            // Custom placeholder styling
                            {...props}
                        />
                        <View
                            ai="center"
                            style={styles.suffixIcon}
                        >
                            {renderSuffixIcon()}
                        </View>
                    </View>
                </View>
                {charLimit && (
                    <Text
                        fw="400"
                        fs="10"
                        color="neutral50"
                        ml={9}
                        mt={4}
                    >
                        {charLimit} maximum characters
                    </Text>
                )}
                {gapBottom ? <View style={{height: gapBottom}} /> : null}
            </>
        );
    },
);

TextInput.displayName = 'TextInput';

const styles = StyleSheet.create({
    container: {
        borderRadius: 16,
        paddingHorizontal: 16,
        paddingVertical: 20,
        position: 'relative',
    },
    inputWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    input: {
        fontSize: 16,
        fontWeight: '400',
        fontFamily: 'MontserratRegular',
        padding: 0,
        margin: 0,
        height: '100%',
    },
    suffixIcon: {
        marginLeft: 8,
        height: '100%',
    },
});
