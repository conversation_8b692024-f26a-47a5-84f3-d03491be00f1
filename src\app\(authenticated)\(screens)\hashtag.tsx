import CloseIcon from '@assets/svgs/close-icon.svg';
import {Text, View, TextInput, Button} from '@components/native';
import {ScreenWrapper} from '@components/shared';
import {StackBackButton} from '@components/shared';
import {useTheme} from '@context';
import {useRouter} from 'expo-router';
import React, {useState} from 'react';
import {ScrollView, Pressable} from 'react-native';

const HashtagScreen = () => {
    const [hashtag, setHashtag] = useState('');
    const [hashtags, setHashtags] = useState<string[]>([]);
    const {colors} = useTheme();
    const router = useRouter();

    const handleAddHashtag = () => {
        if (hashtag.trim() && hashtags.length < 5) {
            setHashtags([...hashtags, hashtag.trim()]);
            setHashtag('');
        }
    };

    const handleChangeText = (text: string) => {
        setHashtag(text.replace(/\s/g, ''));
    };

    const handleRemoveHashtag = (indexToRemove: number) => {
        setHashtags(hashtags.filter((_, index) => index !== indexToRemove));
    };

    return (
        <ScreenWrapper>
            <View
                px={20}
                flex={1}
            >
                <View
                    pb={16}
                    pt={48}
                >
                    <StackBackButton title="Add Hashtags" />
                </View>
                <View
                    flex={1}
                    jc="space-between"
                >
                    <View>
                        <TextInput
                            title="Hashtag"
                            placeholder="Enter hashtag"
                            value={hashtag}
                            onChangeText={handleChangeText}
                            gapBottom={24}
                        />
                        {hashtags.length < 5 && (
                            <Button
                                bw={1}
                                bc="purple500"
                                bg="transparent"
                                color="purple700"
                                isFullWidth={false}
                                onPress={handleAddHashtag}
                                disabled={hashtags.length >= 5}
                                mb={30}
                            >
                                + ADD HASHTAG
                            </Button>
                        )}
                        <ScrollView>
                            <View
                                fd="row"
                                fw="wrap"
                                gap={12}
                                jc="center"
                            >
                                {hashtags.map((tag, index) => (
                                    <View
                                        key={index}
                                        bg="purpleLight"
                                        br={16}
                                        px={16}
                                        py={10}
                                        fd="row"
                                        ai="center"
                                        gap={8}
                                    >
                                        <Text
                                            fw="500"
                                            fs="14"
                                            color="purple700"
                                        >
                                            #{tag}
                                        </Text>
                                        <Pressable
                                            onPress={() => handleRemoveHashtag(index)}
                                            style={{
                                                width: 16,
                                                height: 16,
                                                borderRadius: 8,
                                                backgroundColor: colors.background,
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                            }}
                                        >
                                            <CloseIcon
                                                width={20}
                                                height={20}
                                                color={colors.purple700}
                                            />
                                        </Pressable>
                                    </View>
                                ))}
                            </View>
                        </ScrollView>
                    </View>
                    <Button
                        mb={53}
                        isFullWidth={true}
                        onPress={() => router.back()}
                    >
                        DONE
                    </Button>
                </View>
            </View>
        </ScreenWrapper>
    );
};

export default HashtagScreen;
