import AnxiousDay from '@assets/svgs/anxious-day.svg';
import FrustratedDay from '@assets/svgs/frustrated-day.svg';
import HealingDay from '@assets/svgs/healing-day.svg';
import HeartbrokenDay from '@assets/svgs/heartbroken-day.svg';
import OptimisticDay from '@assets/svgs/optimistic-day.svg';
import {Text, View} from '@components/native';
import {FontFamilyMap} from '@components/native/Text';
import {ScreenWrapper, StackBackButton} from '@components/shared';
import {useTheme} from '@context';
import {useLocalSearchParams} from 'expo-router';
import React, {useMemo} from 'react';
import {CalendarList} from 'react-native-calendars';

interface MoodData {
    [week: string]: {day: number; hour: number; color: string}[];
}

const getMoodSvgByColor = (color: string) => {
    switch (color) {
        case '#595387':
            return HeartbrokenDay;
        case '#FFA04C':
            return FrustratedDay;
        case '#828CFF':
            return AnxiousDay;
        case '#FFFB8C':
            return OptimisticDay;
        case '#FFB7B7':
            return HealingDay;
        default:
            return null;
    }
};

const DayComponent = ({date, marking}: any) => {
    const lastMood = marking?.lastMood;
    const MoodSvg = lastMood ? getMoodSvgByColor(lastMood.color) : null;
    const isToday = date.dateString === new Date().toISOString().split('T')[0];

    return (
        <View
            ai="center"
            jc="center"
        >
            {MoodSvg && (
                <View
                    pos="absolute"
                    w={30}
                    h={27}
                    br={21}
                    ai="center"
                    jc="center"
                    bg={isToday ? 'purpleLight' : 'transparent'}
                >
                    <MoodSvg
                        width={36}
                        height={36}
                    />
                </View>
            )}
            <Text
                fw={isToday ? '600' : '400'}
                fs="16"
                color="neutral80"
            >
                {date.day}
            </Text>
        </View>
    );
};

const SummaryScreen = () => {
    const params = useLocalSearchParams();
    const {colors} = useTheme();

    // Parse moodData from params
    const moodData: MoodData = useMemo(() => {
        try {
            return params.moodData ? JSON.parse(params.moodData as string) : {};
        } catch (error) {
            console.error('Error parsing moodData:', error);
            return {};
        }
    }, [params.moodData]);

    // Helper function to convert week key to date
    const weekKeyToDate = (weekKey: string) => {
        return new Date(weekKey);
    };

    // Helper function to format date as YYYY-MM-DD
    const formatDateString = (date: Date) => {
        return date.toISOString().split('T')[0];
    };

    // Calculate date range and marked dates
    const {markedDates} = useMemo(() => {
        const currentDate = new Date();
        const currentMonth = formatDateString(new Date(currentDate.getFullYear(), currentDate.getMonth(), 1));

        // If no mood data, show only current month
        if (!moodData || Object.keys(moodData).length === 0) {
            return {
                minDate: currentMonth,
                maxDate: currentMonth,
                markedDates: {},
            };
        }

        // Create marked dates object
        const marked: {[key: string]: any} = {};

        // Process each week's mood data
        Object.entries(moodData).forEach(([weekKey, moods]) => {
            if (moods && moods.length > 0) {
                // Get the week start date
                const weekStartDate = weekKeyToDate(weekKey);

                // Mark each day of the week that has mood data
                moods.forEach((mood) => {
                    const moodDate = new Date(weekStartDate);
                    moodDate.setDate(moodDate.getDate() + mood.day + 1);
                    const dateString = formatDateString(moodDate);

                    // Store the last mood for this date (in case of multiple moods per day)
                    if (!marked[dateString] || marked[dateString].lastMood.hour < mood.hour) {
                        marked[dateString] = {
                            lastMood: mood,
                        };
                    }
                });
            }
        });

        return {
            markedDates: marked,
        };
    }, [moodData]);

    return (
        <ScreenWrapper>
            <View
                px={20}
                pb={16}
                pt={48}
            >
                <StackBackButton title="Mood Summary" />
            </View>
            <View
                fd="row"
                jc="space-between"
                ai="center"
                px={34}
            >
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    S
                </Text>
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    M
                </Text>
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    T
                </Text>
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    W
                </Text>
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    T
                </Text>
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    F
                </Text>
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    S
                </Text>
            </View>
            <View
                h={1}
                bg="neutral20"
                mt={10}
            />
            <View flex={1}>
                <CalendarList
                    markedDates={markedDates}
                    dayComponent={DayComponent}
                    disabledByDefault={true}
                    disableAllTouchEventsForDisabledDays={true}
                    disableAllTouchEventsForInactiveDays={true}
                    onDayPress={undefined}
                    onDayLongPress={undefined}
                    pastScrollRange={(() => {
                        const weekKeys = Object.keys(moodData);
                        if (!moodData || weekKeys.length === 0) return 0;
                        const firstMoodDate = weekKeyToDate(weekKeys[0]);
                        const currentDate = new Date();
                        const monthDiff = (currentDate.getFullYear() - firstMoodDate.getFullYear()) * 12 + (currentDate.getMonth() - firstMoodDate.getMonth()) - 1;
                        return Math.max(0, monthDiff);
                    })()}
                    futureScrollRange={0}
                    showScrollIndicator={false}
                    theme={{
                        backgroundColor: colors.background,
                        calendarBackground: colors.background,
                        textDisabledColor: colors.neutral80,
                        monthTextColor: colors.neutral80,
                        textMonthFontFamily: FontFamilyMap['700'],
                        textMonthFontSize: 16,
                    }}
                    calendarStyle={{}}
                    hideArrows={true}
                    disableMonthChange={true}
                    hideExtraDays={true}
                    hideDayNames={true}
                />
            </View>
        </ScreenWrapper>
    );
};

export default SummaryScreen;
